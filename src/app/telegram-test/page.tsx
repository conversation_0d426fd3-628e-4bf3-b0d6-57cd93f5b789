"use client";

import { useTelegramAuth } from "@/hooks/useTelegramAuth";
import { useEffect, useState } from "react";

export default function TelegramTestPage() {
  const { isInTelegram, authenticate, isLoading } = useTelegramAuth({
    onSuccess: () => {
      console.log("Authentication successfull");
    },
    onError: (error) => {
      console.error("Authentication failed:", error);
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    if (typeof window !== "undefined") {
      setDebugInfo({
        hasWindow: typeof window !== "undefined",
        hasTelegramWebApp: !!window.Telegram?.WebApp,
        telegramWebAppExists: window.Telegram?.WebApp !== undefined,
        userAgent: navigator.userAgent,
        isTelegramUserAgent: navigator.userAgent.includes("Telegram"),
        isInTelegram,
        windowTelegram: !!window.Telegram,
        webAppInitData: window.Telegram?.WebApp?.initData || "not available",
        webAppInitDataUnsafe:
          window.Telegram?.WebApp?.initDataUnsafe || "not available",
      });
    }
  }, [isInTelegram]);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Telegram Web App Detection Test
        </h1>

        <div className="space-y-6">
          {/* Detection Status */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Detection Status</h2>
            <div
              className={`text-lg font-bold p-4 rounded-lg ${
                isInTelegram
                  ? "bg-green-100 text-green-800"
                  : "bg-red-100 text-red-800"
              }`}
            >
              {isInTelegram
                ? "✅ Running in Telegram Web App"
                : "❌ NOT in Telegram Web App"}
            </div>
          </div>

          {/* Authentication Test */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Authentication Test</h2>
            <button
              onClick={authenticate}
              disabled={isLoading || !isInTelegram}
              className={`px-6 py-3 rounded-lg font-semibold ${
                isInTelegram
                  ? "bg-blue-500 hover:bg-blue-600 text-white"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              {isLoading ? "Authenticating..." : "Test Authentication"}
            </button>
            {!isInTelegram && (
              <p className="mt-2 text-sm text-gray-600">
                Authentication is only available in Telegram Web App
              </p>
            )}
          </div>

          {/* Debug Information */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
            <pre className="text-sm text-gray-700 overflow-x-auto bg-white p-4 rounded border">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">
              How to Test in Telegram
            </h2>
            <div className="space-y-2 text-blue-800">
              <p>1. Open this URL in a Telegram bot or mini app</p>
              <p>
                2. The detection status should show &ldquo;✅ Running in
                Telegram Web App&quot;
              </p>
              <p>3. The authentication button should be enabled</p>
              <p>4. Check the debug information for detailed detection data</p>
            </div>
            <div className="mt-4 p-3 bg-blue-100 rounded">
              <p className="text-sm text-blue-700">
                <strong>Current URL:</strong>{" "}
                {typeof window !== "undefined"
                  ? window.location.href
                  : "Loading..."}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
