"use client";

import { createBulkOrders } from "@/api/order-api";
import { getUserById, getUsers } from "@/api/user-api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { OrderEntity, OrderStatus } from "@/core.constants";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

const COLLECTION_IDS = [
  "5933543975653737112",
  "5895603153683874485",
  "5913351908466098791",
  "5895518353849582541"
];

// Fallback user IDs if getUsers fails
const FALLBACK_USER_IDS = [
  "FLMmHTWyQeN4E9YLq70Du2U4ySR2",
  "ifwVtoFV3tPmH2bRJGzShhnQ31A2"
];

export function OrderSeeding() {
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedingStatus, setSeedingStatus] = useState<string>("");

  const generateRandomPrice = () => {
    // Generate random price between 1 and 50 TON
    return Math.floor(Math.random() * 49) + 1;
  };

  const seedOrders = async () => {
    try {
      setIsSeeding(true);
      setSeedingStatus("Fetching users...");

      let users: { id: string }[] = [];

      try {
        // Try to get users from Firebase
        const fetchedUsers = await getUsers(100); // Get up to 100 users
        users = fetchedUsers;
      } catch (error) {
        console.warn("Failed to fetch users from database, using fallback user IDs:", error);
        setSeedingStatus("Using fallback user IDs...");

        // Verify fallback users exist
        const validUsers = [];
        for (const userId of FALLBACK_USER_IDS) {
          try {
            const user = await getUserById(userId);
            if (user) {
              validUsers.push({ id: userId });
            }
          } catch (err) {
            console.warn(`Fallback user ${userId} not found:`, err);
          }
        }
        users = validUsers;
      }

      if (users.length < 1) {
        toast.error("No valid users found in the database. Please ensure users exist before seeding orders.");
        return;
      }

      if (users.length === 1) {
        toast.warning("Only 1 user found. Orders will be created with the same user as both buyer and seller.");
      }

      setSeedingStatus("Generating orders...");

      const ordersToCreate: Omit<OrderEntity, "id" | "createdAt" | "updatedAt">[] = [];

      // Create 15 orders with buyerId (buyers looking for sellers)
      for (let i = 0; i < 15; i++) {
        const randomUser = users[Math.floor(Math.random() * users.length)];
        const randomCollectionId = COLLECTION_IDS[Math.floor(Math.random() * COLLECTION_IDS.length)];
        
        ordersToCreate.push({
          buyerId: randomUser.id,
          collectionId: randomCollectionId,
          amount: generateRandomPrice(),
          status: OrderStatus.ACTIVE,
          owned_gift_id: `gift_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        });
      }

      // Create 15 orders with sellerId (sellers looking for buyers)
      for (let i = 0; i < 15; i++) {
        const randomUser = users[Math.floor(Math.random() * users.length)];
        const randomCollectionId = COLLECTION_IDS[Math.floor(Math.random() * COLLECTION_IDS.length)];
        
        ordersToCreate.push({
          sellerId: randomUser.id,
          collectionId: randomCollectionId,
          amount: generateRandomPrice(),
          status: OrderStatus.ACTIVE,
          owned_gift_id: `gift_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        });
      }

      setSeedingStatus("Creating orders in Firebase...");

      // Create orders in bulk
      await createBulkOrders(ordersToCreate);

      setSeedingStatus("Orders seeded successfully!");
      toast.success(`Successfully seeded ${ordersToCreate.length} orders (15 buyer orders + 15 seller orders)`);

    } catch (error) {
      console.error("Error seeding orders:", error);
      toast.error("Failed to seed orders: " + (error as Error).message);
      setSeedingStatus("Error occurred during seeding");
    } finally {
      setIsSeeding(false);
      setTimeout(() => setSeedingStatus(""), 3000);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Order Seeding</CardTitle>
        <CardDescription>
          Seed the database with test orders for development and testing purposes.
          This will create 30 active orders total: 15 with buyerId (buyers looking for sellers) 
          and 15 with sellerId (sellers looking for buyers).
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-medium">Collection IDs to use:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            {COLLECTION_IDS.map((id) => (
              <li key={id} className="font-mono">• {id}</li>
            ))}
          </ul>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium">What will be created:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• 15 orders with buyerId (buyers looking for sellers)</li>
            <li>• 15 orders with sellerId (sellers looking for buyers)</li>
            <li>• All orders will have status: &quot;active&quot;</li>
            <li>• Random prices between 1-50 TON</li>
            <li>• Random collection IDs from the list above</li>
            <li>• Random user assignments from existing users</li>
          </ul>
        </div>

        {seedingStatus && (
          <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-md">
            <p className="text-sm text-blue-700 dark:text-blue-300">{seedingStatus}</p>
          </div>
        )}

        <Button 
          onClick={seedOrders} 
          disabled={isSeeding}
          className="w-full"
        >
          {isSeeding ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Seeding Orders...
            </>
          ) : (
            "Seed Orders"
          )}
        </Button>

        <div className="text-xs text-muted-foreground">
          <strong>Note:</strong> This will create orders using existing users in the database.
          If no users are found, it will use fallback user IDs: FLMmHTWyQeN4E9YLq70Du2U4ySR2 and ifwVtoFV3tPmH2bRJGzShhnQ31A2.
        </div>
      </CardContent>
    </Card>
  );
}
