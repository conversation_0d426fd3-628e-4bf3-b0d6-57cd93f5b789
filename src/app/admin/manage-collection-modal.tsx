"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collection,
  CollectionStatus,
  COLLECTION_STATUS_TEXT,
} from "@/core.constants";
import { createCollection, updateCollection } from "@/api/collection-api";
import { firebaseTimestampToDate } from "@/utils/date-utils";

const collectionSchema = z.object({
  id: z.string().min(1, "Collection ID is required"),
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Description is required"),
  status: z.nativeEnum(CollectionStatus),
  floorPrice: z.number().min(0, "Floor price must be non-negative"),
  launchedAt: z.string().optional(),
});

type CollectionFormData = z.infer<typeof collectionSchema>;

interface ManageCollectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  collection: Collection | null;
  onSave: () => void;
}

export const ManageCollectionModal = ({
  isOpen,
  onClose,
  collection,
  onSave,
}: ManageCollectionModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CollectionFormData>({
    resolver: zodResolver(collectionSchema),
    defaultValues: {
      id: "",
      name: "",
      description: "",
      status: CollectionStatus.PREMARKET,
      floorPrice: 0,
      launchedAt: "",
    },
  });

  useEffect(() => {
    if (collection) {
      console.log("collection.launchedAt", collection.launchedAt);

      // Convert Date to datetime-local string format
      const launchedAtString = collection.launchedAt
        ? new Date(firebaseTimestampToDate(collection.launchedAt))
            .toISOString()
            .slice(0, 16)
        : "";

      reset({
        id: collection.id,
        name: collection.name,
        description: collection.description,
        status: collection.status,
        floorPrice: collection.floorPrice || 0,
        launchedAt: launchedAtString,
      });
    } else {
      reset({
        id: "",
        name: "",
        description: "",
        status: CollectionStatus.PREMARKET,
        floorPrice: 0,
        launchedAt: "",
      });
    }
  }, [collection, reset]);

  const onSubmit = async (data: CollectionFormData) => {
    setIsSubmitting(true);
    try {
      // Handle launchedAt field logic
      const updateData: Partial<Collection> = { ...data };

      // Convert launchedAt string to Date if provided
      if (data.launchedAt) {
        updateData.launchedAt = new Date(data.launchedAt);
      }

      // If changing status from PREMARKET to MARKET and launchedAt is not set
      if (
        !data.launchedAt &&
        data.status === CollectionStatus.MARKET &&
        collection?.status === CollectionStatus.PREMARKET &&
        !collection.launchedAt
      ) {
        updateData.launchedAt = new Date();
      }

      if (collection) {
        await updateCollection(collection.id, updateData);
      } else {
        await createCollection(data);
      }

      onSave();
      onClose();
    } catch (error) {
      console.error("Error saving collection:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {collection ? "Edit Collection" : "Add Collection"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="id">Collection ID</Label>
            <Input
              id="id"
              {...register("id")}
              placeholder="Enter collection ID"
            />
            {errors.id && (
              <p className="text-sm text-red-600">{errors.id.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Enter collection name"
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter collection description"
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-red-600">
                {errors.description.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="floorPrice">Floor Price (TON)</Label>
            <Input
              id="floorPrice"
              type="number"
              step="0.01"
              min="0"
              {...register("floorPrice", { valueAsNumber: true })}
              placeholder="Enter minimum floor price"
            />
            {errors.floorPrice && (
              <p className="text-sm text-red-600">
                {errors.floorPrice.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={watch("status")}
              onValueChange={(value) =>
                setValue("status", value as CollectionStatus)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(CollectionStatus).map((status) => (
                  <SelectItem key={status} value={status}>
                    {COLLECTION_STATUS_TEXT[status]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.status && (
              <p className="text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="launchedAt">Launched At</Label>
            <Input
              id="launchedAt"
              type="datetime-local"
              {...register("launchedAt")}
              placeholder="Select launch date and time"
            />
            {errors.launchedAt && (
              <p className="text-sm text-red-600">
                {errors.launchedAt.message}
              </p>
            )}
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : collection ? "Update" : "Create"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
