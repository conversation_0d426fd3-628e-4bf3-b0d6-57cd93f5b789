"use client";

import React from "react";
import { OrderCard } from "./order-card";
import { GridItem } from "@/components/ui/virtualized-grid";
import { Collection, OrderEntity } from "@/core.constants";

interface VirtualizedOrderCardProps {
  order: OrderEntity;
  collection: Collection | undefined;
  onClick: () => void;
  animated?: boolean;
  index: number;
  initialRenderedCount?: number;
}

export const VirtualizedOrderCard: React.FC<VirtualizedOrderCardProps> = ({
  order,
  collection,
  onClick,
  animated,
  index,
  initialRenderedCount = 15,
}) => {
  const itemId = `order-${order.id}`;

  return (
    <GridItem
      itemId={itemId}
      index={index}
      initialRenderedCount={initialRenderedCount}
    >
      <OrderCard
        animated={animated}
        order={order}
        collection={collection}
        onClick={onClick}
      />
    </GridItem>
  );
};
