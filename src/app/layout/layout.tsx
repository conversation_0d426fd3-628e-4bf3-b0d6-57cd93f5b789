import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "../globals.css";
import { RootProvider } from "@/root-context";
import TonLayout from "../ton-layout";
import { Toaster } from "sonner";
import { Root } from "@/components/Root";
import Header from "./header";
import Footer from "./footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

if (!Object.hasOwn) {
  Object.hasOwn = function (obj, prop) {
    return Object.prototype.hasOwnProperty.call(obj, prop);
  };
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <head></head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-[#17212b] text-[#f5f5f5]`}
        suppressHydrationWarning
      >
        <RootProvider>
          <Root>
            <Header />
            <TonLayout>
              <div className="min-h-screen flex flex-col pt-[72px] pb-16">
                <main className="flex-1 p-2">
                  <div className="max-w-6xl mx-auto">{children}</div>
                </main>
              </div>
            </TonLayout>
            <Toaster />
            <Footer />
          </Root>
        </RootProvider>
      </body>
    </html>
  );
}
