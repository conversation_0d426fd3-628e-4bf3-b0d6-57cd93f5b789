"use client";

import TelegramAuth from "@/components/telegram-auth";
import { useDidMount } from "@/hooks/useDidMount";
import { useRouter } from "next/navigation";

export default function TelegramAuthPage() {
  const didMount = useDidMount();
  const router = useRouter();

  if (!didMount) {
    return null;
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-12 p-6 border rounded-lg bg-gradient-to-r from-blue-50 to-cyan-50">
          <TelegramAuth
            onSuccess={() => {
              router.push("/profile");
            }}
            onError={(error) => {
              alert(`Authentication failed: ${error}`);
            }}
          />
        </div>
        <div className="mb-8 p-4 bg-gray-100 rounded-lg">
          <h3 className="text-lg font-semibold mb-3">Environment Variables:</h3>
          <pre className="text-sm bg-gray-800 text-green-400 p-4 rounded overflow-x-auto">
            {`NEXT_PUBLIC_FIREBASE_API_KEY=${process.env.NEXT_PUBLIC_FIREBASE_API_KEY}
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=${process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN}
NEXT_PUBLIC_FIREBASE_PROJECT_ID=${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=${process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET}
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}
NEXT_PUBLIC_FIREBASE_APP_ID=${process.env.NEXT_PUBLIC_FIREBASE_APP_ID}

# TON Configuration
NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS=${process.env.NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS}`}
          </pre>
        </div>
      </div>
    </div>
  );
}
