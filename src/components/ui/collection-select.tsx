"use client";

import * as React from "react";
import { Check, ChevronDown, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Drawer } from "vaul";
import { Collection } from "@/core.constants";
import Image from "next/image";
import { TgsOrImage } from "../TgsOrImage";
import { ItemCacheProvider, GridItem } from "@/components/ui/virtualized-grid";
import { getImagePathWithFallback } from "@/utils/image-path";

const tguiClasses = 'tgui-c3e2e598bd70eee6 tgui-080a44e6ac3f4d27 tgui-809f1f8a3f64154d tgui-266b6ffdbad2b90e tgui-8f63cd31b2513281 tgui-9f9a52f695b85cc9';

interface CollectionSelectProps {
  animated?: boolean;
  collections: Collection[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const CollectionSelectComponent = function CollectionSelect({
  animated,
  collections,
  value,
  onValueChange,
  placeholder = "Select collection...",
  className,
}: CollectionSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  const filteredCollections = React.useMemo(() => {
    if (!searchQuery) return collections;
    return collections.filter((collection) =>
      collection.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [collections, searchQuery]);

  const selectedCollection = React.useMemo(() =>
    collections.find((collection) => collection.id === value),
    [collections, value]
  );

  const handleSelect = React.useCallback((collectionId: string) => {
    onValueChange(collectionId);
    setOpen(false);
    setSearchQuery("");
  }, [onValueChange]);

  const handleOpenChange = React.useCallback((newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchQuery("");
    }
  }, []);

  return (
    <>
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={open}
        onClick={() => setOpen(true)}
        className={cn(
          "w-full justify-between h-9 px-3 py-2 text-sm bg-transparent border-gray-600 text-[var(--tgui--secondary_hint_color)]",
          "hover:bg-[var(--tgui--bg_color)] hover:text-[var(--tgui--secondary_hint_color)]",
          open && "text-[var(--ring)] shadow-[0_0_0_2px_var(--ring)]!",
          !value && "text-gray-400",
          'rounded-[14px] shadow-[0_0_0_2px_var(--tgui--outline)] border-transparent relative',
          className
        )}
      >
        <h6 className={cn("px-[6px] bg-[var(--tgui--bg_color)] absolute -top-3 left-7", tguiClasses)}>Collection</h6>
        {selectedCollection ? (
          <div className="flex items-center gap-2 min-w-0">
            <div className="relative w-6 h-6 rounded-sm overflow-hidden bg-slate-700 flex-shrink-0">
              <Image
                src={getImagePathWithFallback(selectedCollection.id, 'png').primary}
                alt={selectedCollection.name}
                fill
                className="object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  // Try fallback on error
                  const fallbackSrc = getImagePathWithFallback(selectedCollection.id, 'png').fallback;
                  if (target.src !== fallbackSrc) {
                    target.src = fallbackSrc;
                  } else {
                    target.style.display = "none";
                  }
                }}
              />
            </div>
            <span className={cn(tguiClasses,"!font-[var(--tgui--font_weight--accent3)] truncate text-white")}>
              {selectedCollection.name}
            </span>
          </div>
        ) : (
          <span className={cn(tguiClasses,"!font-[var(--tgui--font_weight--accent3)] truncate text-white")}>{placeholder}</span>
        )}
        <ChevronDown
          className={cn(
            "ml-2 h-4 w-4 shrink-0 opacity-50 text-gray-400 transition-transform",
            open && "rotate-180"
          )}
        />
      </Button>

      <Drawer.Root
        open={open}
        onOpenChange={handleOpenChange}
        shouldScaleBackground
        modal={true}
        dismissible={true}
      >
        <Drawer.Portal>
          <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
          <Drawer.Content className="bg-[#232e3c] flex flex-col rounded-t-[10px] mt-24 fixed bottom-0 left-0 right-0 z-[101] border-t border-[#3a4a5c] outline-none focus:outline-none">
            <div className="p-4 bg-[#232e3c] rounded-t-[10px] flex-1 max-h-[85vh] overflow-y-auto">
              <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-8 cursor-grab active:cursor-grabbing touch-manipulation" />

              <div className="max-w-md mx-auto pb-8 px-2 w-full">
                <Drawer.Title className="font-medium mb-4 text-lg text-white">
                  Select Collection
                </Drawer.Title>

                <div className="flex flex-col space-y-4 min-h-0">
                  {/* Search Input */}
                  <div className="flex items-center border border-[#3a4a5c] rounded-lg px-3 py-2 bg-[#17212b] flex-shrink-0">
                    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50 text-gray-400" />
                    <Input
                      placeholder="Search collections..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 h-8 bg-transparent text-white placeholder:text-gray-400"
                    />
                  </div>

                  {/* Collections Grid */}
                  <div className="overflow-y-auto max-h-96 scrollbar-thin">
                    <ItemCacheProvider>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                  {/* All Collections option */}
                  <div
                    className={cn(
                      "relative flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all duration-200 border min-h-[120px] group",
                      value === "all"
                        ? "bg-[#2a3441] border-[#0098EA]"
                        : "bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] hover:border-[#0098EA]/50"
                    )}
                    onClick={() => handleSelect("all")}
                  >
                    <div className="w-full aspect-square rounded-lg bg-[#17212b] flex items-center justify-center mb-2">
                      <span className="text-lg text-white font-bold">All</span>
                    </div>
                    <span className="text-xs text-center text-white font-medium leading-tight truncate w-full">
                      All Collections
                    </span>
                    {value === "all" && (
                      <div className="absolute top-2 right-2">
                        <Check className="h-4 w-4 text-[#0098EA]" />
                      </div>
                    )}
                  </div>

                  {/* Collection tiles */}
                  {filteredCollections.length === 0 && searchQuery ? (
                    <div className="col-span-full py-8 text-center text-sm text-gray-400">
                      No collections found matching &quot;{searchQuery}&quot;.
                    </div>
                  ) : (
                    filteredCollections.map((collection, index) => (
                      <GridItem
                        key={collection.id}
                        itemId={`collection-${collection.id}`}
                        index={index}
                        initialRenderedCount={12}
                      >
                        <div
                          className={cn(
                            "relative flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all duration-150 border min-h-[120px] group",
                            "will-change-transform transform-gpu",
                            value === collection.id
                              ? "bg-[#2a3441] border-[#0098EA]"
                              : "bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] hover:border-[#0098EA]/50"
                          )}
                          onClick={() => handleSelect(collection.id)}
                        >
                          <div className="w-full aspect-square relative rounded-lg overflow-hidden bg-[#17212b] mb-2">
                            <TgsOrImage
                              isImage={!animated}
                              collectionId={collection.id}
                              imageProps={{
                                alt: collection.name,
                                fill: true,
                                className: "object-cover group-hover:scale-105 transition-transform duration-200",
                                loading: "lazy",
                                sizes: "(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw",
                                onError: (e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = "none";
                                }
                              }}
                              tgsProps={{
                                style: {
                                  height: "auto",
                                  width: "auto",
                                  padding: "16px",
                                }
                              }}
                            />
                          </div>
                          <span className="text-xs text-center text-white font-medium leading-tight truncate w-full">
                            {collection.name}
                          </span>
                          {value === collection.id && (
                            <div className="absolute top-2 right-2">
                              <Check className="h-4 w-4 text-[#0098EA]" />
                            </div>
                          )}
                        </div>
                      </GridItem>
                    ))
                  )}
                      </div>
                    </ItemCacheProvider>
                  </div>
                </div>
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
    </>
  );
};

export const CollectionSelect = React.memo(CollectionSelectComponent);
