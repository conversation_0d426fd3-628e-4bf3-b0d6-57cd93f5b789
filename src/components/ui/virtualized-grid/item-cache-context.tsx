"use client";

import React, { createContext, useContext, useRef, useCallback, useState, useEffect } from "react";

interface CacheSignal {
  loadIntersecting: boolean;
  wrapperHeight: number;
  fixed: boolean;
}

interface ItemCacheContextValue {
  loadIntersectionObserver: IntersectionObserver;
  resizeObserver: ResizeObserver;
  getCacheSignal: (cacheKey: string) => CacheSignal;
  subscribeToCacheSignal: (cacheKey: string, callback: (signal: CacheSignal) => void) => () => void;
}

const ItemCacheContext = createContext<ItemCacheContextValue | null>(null);

const DEFAULT_HEIGHT = 200;
const LOAD_MARGIN = "2000px";

export const ItemCacheProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const signalMap = useRef(new Map<string, CacheSignal>());
  const subscribersMap = useRef(new Map<string, Set<(signal: CacheSignal) => void>>());
  const observersRef = useRef<{
    loadIntersectionObserver?: IntersectionObserver;
    resizeObserver?: ResizeObserver;
  }>({});

  const notifySubscribers = useCallback((cacheKey: string, signal: CacheSignal) => {
    const subscribers = subscribersMap.current.get(cacheKey);
    if (subscribers) {
      subscribers.forEach(callback => callback(signal));
    }
  }, []);

  const getCacheSignal = useCallback((cacheKey: string): CacheSignal => {
    let signal = signalMap.current.get(cacheKey);
    if (!signal) {
      signal = {
        loadIntersecting: false,
        wrapperHeight: DEFAULT_HEIGHT,
        fixed: false,
      };
      signalMap.current.set(cacheKey, signal);
    }
    return signal;
  }, []);

  const subscribeToCacheSignal = useCallback((cacheKey: string, callback: (signal: CacheSignal) => void) => {
    if (!subscribersMap.current.has(cacheKey)) {
      subscribersMap.current.set(cacheKey, new Set());
    }
    subscribersMap.current.get(cacheKey)!.add(callback);

    return () => {
      const subscribers = subscribersMap.current.get(cacheKey);
      if (subscribers) {
        subscribers.delete(callback);
        if (subscribers.size === 0) {
          subscribersMap.current.delete(cacheKey);
        }
      }
    };
  }, []);

  // Initialize intersection observer with large root margin for preloading
  if (!observersRef.current.loadIntersectionObserver) {
    observersRef.current.loadIntersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const cacheKey = entry.target.getAttribute("data-cache-key");
          if (cacheKey) {
            const cacheSignal = getCacheSignal(cacheKey);
            if (cacheSignal.loadIntersecting !== entry.isIntersecting) {
              const newSignal = { ...cacheSignal, loadIntersecting: entry.isIntersecting };
              signalMap.current.set(cacheKey, newSignal);
              notifySubscribers(cacheKey, newSignal);
            }
          }
        });
      },
      {
        threshold: 0,
        rootMargin: LOAD_MARGIN,
        root: null,
      }
    );
  }

  // Initialize resize observer to cache dimensions
  if (!observersRef.current.resizeObserver) {
    observersRef.current.resizeObserver = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        const cacheKey = entry.target.getAttribute("data-cache-key");
        if (cacheKey) {
          const cacheSignal = getCacheSignal(cacheKey);
          if (cacheSignal.loadIntersecting) {
            const height = Math.ceil(entry.contentRect.height);
            if (height > 0 && cacheSignal.wrapperHeight !== height) {
              const newSignal = { ...cacheSignal, wrapperHeight: height };
              signalMap.current.set(cacheKey, newSignal);
              notifySubscribers(cacheKey, newSignal);
            }
          }
        }
      });
    });
  }

  const contextValue: ItemCacheContextValue = {
    loadIntersectionObserver: observersRef.current.loadIntersectionObserver!,
    resizeObserver: observersRef.current.resizeObserver!,
    getCacheSignal,
    subscribeToCacheSignal,
  };

  return (
    <ItemCacheContext.Provider value={contextValue}>
      {children}
    </ItemCacheContext.Provider>
  );
};

export const useItemCacheContext = (itemId: string) => {
  const context = useContext(ItemCacheContext);
  if (!context) {
    throw new Error("useItemCacheContext must be used within ItemCacheProvider");
  }

  const [cacheSignal, setCacheSignal] = useState(() => context.getCacheSignal(itemId));

  useEffect(() => {
    const unsubscribe = context.subscribeToCacheSignal(itemId, setCacheSignal);
    return unsubscribe;
  }, [context, itemId]);

  return {
    loadIntersectionObserver: context.loadIntersectionObserver,
    resizeObserver: context.resizeObserver,
    loadIntersecting: cacheSignal.loadIntersecting,
    wrapperHeight: cacheSignal.wrapperHeight,
    fixed: cacheSignal.fixed,
  };
};

export { DEFAULT_HEIGHT };
