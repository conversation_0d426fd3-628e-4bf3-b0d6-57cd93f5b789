"use client";

import React from "react";
import { ItemCacheProvider } from "./item-cache-context";
import { ItemObserver } from "./item-observer";
import { cn } from "@/lib/utils";

interface VirtualizedGridProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  getItemId: (item: T, index: number) => string;
  columns?: number;
  gap?: string;
  className?: string;
  initialRenderedCount?: number;
  itemClassName?: string;
}

const INITIAL_RENDERED_COUNT = 15;

function VirtualizedGridInner<T>({
  items,
  renderItem,
  getItemId,
  columns = 3,
  gap = "16px",
  className = "",
  initialRenderedCount = INITIAL_RENDERED_COUNT,
  itemClassName = "",
}: VirtualizedGridProps<T>) {
  return (
    <div
      className={cn("grid", className)}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap,
      }}
    >
      {items.map((item, index) => {
        const itemId = getItemId(item, index);
        const initialIsIntersecting = index < initialRenderedCount;

        return (
          <ItemObserver
            key={itemId}
            itemId={itemId}
            initialIsIntersecting={initialIsIntersecting}
            className={itemClassName}
          >
            {renderItem(item, index)}
          </ItemObserver>
        );
      })}
    </div>
  );
}

export function VirtualizedGrid<T>(props: VirtualizedGridProps<T>) {
  return (
    <ItemCacheProvider>
      <VirtualizedGridInner {...props} />
    </ItemCacheProvider>
  );
}

// Grid item wrapper component for easier usage
interface GridItemProps {
  children: React.ReactNode;
  itemId: string;
  index: number;
  initialRenderedCount?: number;
  className?: string;
}

export const GridItem: React.FC<GridItemProps> = ({
  children,
  itemId,
  index,
  initialRenderedCount = INITIAL_RENDERED_COUNT,
  className = "",
}) => {
  const initialIsIntersecting = index < initialRenderedCount;

  return (
    <ItemObserver
      itemId={itemId}
      initialIsIntersecting={initialIsIntersecting}
      className={className}
    >
      {children}
    </ItemObserver>
  );
};
