import { useEffect, useRef, useCallback } from 'react';

interface UseInfiniteScrollOptions {
  hasMore: boolean;
  loading: boolean;
  onLoadMore: () => void;
  threshold?: number;
  rootMargin?: string;
}

export const useInfiniteScroll = ({
  hasMore,
  loading,
  onLoadMore,
  threshold = 0.1,
  rootMargin = "100px"
}: UseInfiniteScrollOptions) => {
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    console.log("🔍 Intersection detected:", {
      isIntersecting: entry.isIntersecting,
      hasMore,
      loading,
      target: entry.target,
      boundingRect: entry.boundingClientRect,
      intersectionRatio: entry.intersectionRatio
    });
    if (entry.isIntersecting && hasMore && !loading) {
      console.log("🔄 Infinite scroll triggered - loading more!");
      onLoadMore();
    }
  }, [hasMore, loading, onLoadMore]);

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      threshold,
      rootMargin,
      root: null
    });

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      console.log("🔍 Setting up intersection observer for:", currentRef);
      observer.observe(currentRef);
    } else {
      console.log("⚠️ No ref available for intersection observer");
    }

    return () => {
      if (currentRef) {
        console.log("🧹 Cleaning up intersection observer");
        observer.unobserve(currentRef);
      }
    };
  }, [handleIntersection, threshold, rootMargin]);

  return loadMoreRef;
};
