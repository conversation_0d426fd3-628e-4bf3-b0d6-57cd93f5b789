const REFERRAL_ID_KEY = "marketplace_referral_id";

export function extractReferralIdFromUrl(): string | null {
  if (typeof window === "undefined") {
    return null;
  }

  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get("referral_id");
}

export function extractReferralIdFromTelegram(): string | null {
  if (typeof window === "undefined") {
    return null;
  }

  // Check if we're in Telegram WebApp environment
  // @ts-expect-error note
  if (window.Telegram?.WebApp?.initDataUnsafe?.start_param) {
    // @ts-expect-error note
    const startParam = window.Telegram.WebApp.initDataUnsafe.start_param;

    // Check if start parameter is a referral link (format: ref_<tg_id>)
    if (startParam.startsWith("ref_")) {
      return startParam.replace("ref_", "");
    }
  }

  return null;
}

export function storeReferralId(referralId: string): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    localStorage.setItem(REFERRAL_ID_KEY, referralId);
    console.log("Referral ID stored:", referralId);
  } catch (error) {
    console.error("Failed to store referral ID:", error);
  }
}

export function getStoredReferralId(): string | null {
  if (typeof window === "undefined") {
    return null;
  }

  try {
    return localStorage.getItem(REFERRAL_ID_KEY);
  } catch (error) {
    console.error("Failed to get stored referral ID:", error);
    return null;
  }
}

export function clearStoredReferralId(): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    localStorage.removeItem(REFERRAL_ID_KEY);
    console.log("Referral ID cleared");
  } catch (error) {
    console.error("Failed to clear referral ID:", error);
  }
}

export function handleReferralFromUrl(): string | null {
  // Try to get referral ID from URL parameters first, fallback to Telegram start parameters
  let referralId = extractReferralIdFromUrl();
  referralId ??= extractReferralIdFromTelegram();

  if (referralId) {
    // Only store if we don't already have one stored
    const existingReferralId = getStoredReferralId();
    if (!existingReferralId) {
      storeReferralId(referralId);
      console.log("New referral ID detected and stored:", referralId);
    } else {
      console.log(
        "Referral ID already exists, not overwriting:",
        existingReferralId
      );
    }
    return referralId;
  }

  return null;
}

export function consumeReferralId(): string | null {
  const referralId = getStoredReferralId();
  if (referralId) {
    clearStoredReferralId();
    console.log("Referral ID consumed:", referralId);
  }
  return referralId;
}
