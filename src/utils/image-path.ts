interface ImagePathOptions {
  collectionId: string;
  format?: 'png' | 'tgs';
}

interface ImagePathResult {
  src: string;
  isCdn: boolean;
}

const CDN_BASE_URL = 'https://cdn.changes.tg/gifts/originals';

// Cache for CDN availability checks
const cdnAvailabilityCache = new Map<string, { available: boolean; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < CACHE_DURATION;
};

const checkCdnAvailability = async (url: string): Promise<boolean> => {
  const cacheKey = url;
  const cached = cdnAvailabilityCache.get(cacheKey);
  
  if (cached && isCacheValid(cached.timestamp)) {
    return cached.available;
  }

  try {
    const response = await fetch(url, { method: 'HEAD' });
    const available = response.ok;
    
    cdnAvailabilityCache.set(cacheKey, {
      available,
      timestamp: Date.now()
    });
    
    return available;
  } catch (error) {
    console.warn(`CDN availability check failed for ${url}:`, error);
    cdnAvailabilityCache.set(cacheKey, {
      available: false,
      timestamp: Date.now()
    });
    return false;
  }
};

/**
 * Get image path, trying CDN first and falling back to local path
 */
export const getImagePath = async ({ collectionId, format = 'png' }: ImagePathOptions): Promise<ImagePathResult> => {
  if (!collectionId) {
    return {
      src: '',
      isCdn: false
    };
  }

  const cdnUrl = `${CDN_BASE_URL}/${collectionId}`;
  const localPath = `/limited/${collectionId}/Original.${format}`;

  // Try CDN first
  const isCdnAvailable = await checkCdnAvailability(cdnUrl);
  
  if (isCdnAvailable) {
    return {
      src: cdnUrl,
      isCdn: true
    };
  }

  // Fall back to local path
  return {
    src: localPath,
    isCdn: false
  };
};

/**
 * Synchronous version that returns CDN URL first, with fallback handling in component
 */
export const getImageSrc = (collectionId: string): string => {
  if (!collectionId) return '';
  return `/limited/${collectionId}/Original.png`;
};

/**
 * Synchronous version for TGS files
 */
export const getTgsUrl = (collectionId: string): string => {
  if (!collectionId) return '';
  return `/limited/${collectionId}/Original.tgs`;
};

/**
 * Get CDN URL for images
 */
export const getCdnImageUrl = (collectionId: string): string => {
  if (!collectionId) return '';
  return `${CDN_BASE_URL}/${collectionId}`;
};

/**
 * Clear the CDN availability cache
 */
export const clearImagePathCache = (): void => {
  cdnAvailabilityCache.clear();
};

/**
 * Enhanced image path function with CDN fallback
 * Returns CDN URL first, then local path as fallback
 */
export const getImagePathWithFallback = (collectionId: string, format: 'png' | 'tgs' = 'png') => {
  if (!collectionId) return { primary: '', fallback: '' };

  const cdnUrl = `${CDN_BASE_URL}/${collectionId}/Original.${format}`;
  const localPath = `/limited/${collectionId}/Original.${format}`;

  return {
    primary: cdnUrl,
    fallback: localPath
  };
};
