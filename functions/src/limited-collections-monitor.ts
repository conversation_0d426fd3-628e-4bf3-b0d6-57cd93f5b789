import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import { Collection, CollectionStatus, OrderEntity } from "./types";
import {
  getTelegramBotToken,
  getTelegramApiId,
  getTelegramApiHash,
} from "./config";

const db = admin.firestore();

// 30 days in milliseconds
const DEADLINE_DAYS = 30;
const DEADLINE_MS = DEADLINE_DAYS * 24 * 60 * 60 * 1000;

interface LimitedGift {
  id: string;
  limited: boolean;
  upgradeStars: number | null;
}

async function fetchLimitedCollections(): Promise<LimitedGift[]> {
  const botToken = getTelegramBotToken();
  const apiId = getTelegramApiId();
  const apiHash = getTelegramApiHash();

  const stringSession = new StringSession("");
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  try {
    await client.start({
      botAuthToken: botToken,
    });

    const result = await client.invoke(
      new Api.payments.GetStarGifts({ hash: 0 })
    );

    // Handle the result properly based on its type
    if (!("gifts" in result)) {
      console.log("No gifts found in result");
      return [];
    }

    const limitedGifts = (result as any).gifts
      .filter((gift: any) => gift.limited === true)
      .map((gift: any) => ({
        id: gift.id.toString(),
        limited: gift.limited,
        upgradeStars: gift.upgradeStars ? gift.upgradeStars.toString() : null,
      }));

    console.log(`Found ${limitedGifts.length} limited gifts from Telegram API`);
    return limitedGifts;
  } catch (error) {
    console.error("Error fetching limited collections from Telegram:", error);
    throw error;
  } finally {
    await client.disconnect();
  }
}

async function updateCollectionToMarket(collectionId: string): Promise<void> {
  const collectionRef = db.collection("collections").doc(collectionId);

  await collectionRef.update({
    status: CollectionStatus.MARKET,
    launchedAt: admin.firestore.Timestamp.now(),
  });

  console.log(`Updated collection ${collectionId} to MARKET status`);
}

async function addDeadlineToOrders(collectionId: string): Promise<void> {
  const ordersQuery = db
    .collection("orders")
    .where("collectionId", "==", collectionId);

  const ordersSnapshot = await ordersQuery.get();

  if (ordersSnapshot.empty) {
    console.log(`No orders found for collection ${collectionId}`);
    return;
  }

  // Filter orders that need deadline updates
  const ordersToUpdate = ordersSnapshot.docs.filter((orderDoc) => {
    const order = orderDoc.data() as OrderEntity;
    return !order.deadline;
  });

  if (ordersToUpdate.length === 0) {
    console.log(
      `No orders needed deadline updates for collection ${collectionId}`
    );
    return;
  }

  // Process orders in chunks to avoid batch size limits (500 operations max)
  const BATCH_SIZE = 400; // Use 400 to be safe
  let totalUpdatedCount = 0;

  for (let i = 0; i < ordersToUpdate.length; i += BATCH_SIZE) {
    const chunk = ordersToUpdate.slice(i, i + BATCH_SIZE);
    const batch = db.batch();

    for (const orderDoc of chunk) {
      // Add 30-day deadline from current date
      const deadline = new Date(Date.now() + DEADLINE_MS);

      batch.update(orderDoc.ref, {
        deadline: admin.firestore.Timestamp.fromDate(deadline),
      });

      // TODO: Add notification logic to notify buyer and seller about deadlines
    }

    await batch.commit();
    totalUpdatedCount += chunk.length;

    console.log(
      `Processed batch ${Math.floor(i / BATCH_SIZE) + 1}: Updated ${
        chunk.length
      } orders`
    );
  }

  console.log(
    `Added deadlines to ${totalUpdatedCount} orders for collection ${collectionId}`
  );
}

async function processUpgradeableCollection(gift: LimitedGift): Promise<void> {
  try {
    const collectionRef = db.collection("collections").doc(gift.id);
    const collectionDoc = await collectionRef.get();

    if (!collectionDoc.exists) {
      console.log(`Collection ${gift.id} not found in Firestore, skipping`);
      return;
    }

    const collection = collectionDoc.data() as Collection;

    // Skip if not PREMARKET status
    if (collection.status !== CollectionStatus.PREMARKET) {
      console.log(
        `Collection ${gift.id} status is ${collection.status}, skipping`
      );
      return;
    }

    console.log(
      `Processing collection ${gift.id} with upgradeStars: ${gift.upgradeStars}`
    );

    // Update collection status to MARKET and set launchedAt
    await updateCollectionToMarket(gift.id);

    // Add deadlines to orders without them
    await addDeadlineToOrders(gift.id);

    console.log(`Successfully processed collection ${gift.id}`);
  } catch (error) {
    console.error(`Error processing collection ${gift.id}:`, error);
    throw error;
  }
}

export async function checkLimitedCollections(): Promise<void> {
  try {
    console.log("Starting limited collections check...");

    // Fetch limited collections from Telegram API
    const limitedGifts = await fetchLimitedCollections();

    // Filter for collections with upgradeStars !== null
    const upgradeableGifts = limitedGifts.filter(
      (gift) => gift.upgradeStars !== null
    );

    if (upgradeableGifts.length === 0) {
      console.log("No upgradeable limited collections found");
      return;
    }

    console.log(
      `Found ${upgradeableGifts.length} upgradeable limited collections`
    );

    // Process each upgradeable collection
    for (const gift of upgradeableGifts) {
      try {
        await processUpgradeableCollection(gift);
      } catch (error) {
        console.error(`Failed to process collection ${gift.id}:`, error);
        // Continue processing other collections even if one fails
      }
    }

    console.log("Limited collections check completed");
  } catch (error) {
    console.error("Error in checkLimitedCollections:", error);
    throw error;
  }
}

export const limitedCollectionsMonitor = onSchedule(
  {
    schedule: "0 1 * * *", // Run daily at 1 AM UTC
    timeZone: "UTC",
  },
  async () => {
    try {
      console.log(
        "Limited collections monitor triggered at:",
        new Date().toISOString()
      );
      await checkLimitedCollections();
      console.log("Limited collections monitor completed successfully");
    } catch (error) {
      console.error("Limited collections monitor failed:", error);
    }
  }
);
