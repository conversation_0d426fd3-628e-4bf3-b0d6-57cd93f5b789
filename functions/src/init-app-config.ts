import * as admin from "firebase-admin";
import { on<PERSON><PERSON>, HttpsError } from "firebase-functions/v2/https";
import { initializeCounter } from "./counter-service";

export const initAppConfig = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  try {
    const db = admin.firestore();
    const userDoc = await db.collection("users").doc(request.auth.uid).get();

    if (!userDoc.exists) {
      throw new HttpsError("not-found", "User not found.");
    }

    const userData = userDoc.data();
    if (userData?.role !== "admin") {
      throw new HttpsError(
        "permission-denied",
        "Only admin users can initialize app config."
      );
    }

    // Initialize order number counter
    await initializeCounter("order_number", 0);

    return {
      success: true,
      message: "App config initialized successfully",
    };
  } catch (error) {
    console.error("Error in initAppConfig function:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while initializing app config."
    );
  }
});
