import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import { on<PERSON>all, HttpsError } from "firebase-functions/v2/https";
import { onSchedule } from "firebase-functions/v2/scheduler";

import { UserEntity } from "./types";
import { monitorTonTransactions } from "./ton-monitor";
import { getConfig } from "./config";

if (!admin.apps.length) {
  const config = getConfig();

  // For Firebase Functions, we need to explicitly set the service account
  // to ensure it has the necessary permissions for custom token creation
  const serviceAccount = config.firebase?.service_account_key;

  if (serviceAccount) {
    // Use service account key if provided
    admin.initializeApp({
      credential: admin.credential.cert(JSON.parse(serviceAccount)),
      projectId: config.app.project_id,
    });
    console.log("Firebase initialized with service account key");
  } else {
    // Use default credentials with explicit project ID
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: config.app.project_id,
    });
    console.log("Firebase initialized with application default credentials");
  }
}

// Configure Firestore settings
admin.firestore().settings({
  ignoreUndefinedProperties: true,
});

const db = admin.firestore();

export const createUserRecord = functions.auth.user().onCreate(async (user) => {
  const userRecord: UserEntity = {
    id: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    role: "user",
    balance: { sum: 0, locked: 0 },
  };

  try {
    await db.collection("users").doc(user.uid).set(userRecord);
    console.log(`User record created for ${user.uid}`);
  } catch (error) {
    console.error("Error creating user record:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating user record."
    );
  }
});

export const getUserByTelegramId = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { tgId } = request.data;

  if (!tgId) {
    throw new HttpsError("invalid-argument", "Telegram ID is required.");
  }

  try {
    const usersQuery = await db
      .collection("users")
      .where("tg_id", "==", tgId)
      .limit(1)
      .get();

    if (usersQuery.empty) {
      throw new HttpsError(
        "not-found",
        "User with this Telegram ID not found."
      );
    }

    const userDoc = usersQuery.docs[0];
    return {
      id: userDoc.id,
      ...userDoc.data(),
    };
  } catch (error) {
    console.error("Error getting user by Telegram ID:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ??
        "Server error while getting user by Telegram ID."
    );
  }
});

export const tonTransactionMonitor = onSchedule(
  {
    schedule: "* * * * *",
    timeZone: "UTC",
  },
  async () => {
    try {
      console.log(
        "TON transaction monitor triggered at:",
        new Date().toISOString()
      );
      await monitorTonTransactions();
      console.log("TON transaction monitor completed successfully");
    } catch (error) {
      console.error("TON transaction monitor failed:", error);
    }
  }
);

export { expiredOrdersMonitor } from "./expired-orders-monitor";

export { limitedCollectionsMonitor } from "./limited-collections-monitor";

export {
  getOrderByIdByBot,
  getUserOrdersByBot,
  sendGiftToRelayerByBot,
  completePurchaseByBot,
} from "./order-functions/bot-order-functions";

export {
  createOrderAsBuyer,
  makePurchaseAsBuyer,
} from "./order-functions/buyer-order-functions";

export {
  createOrderAsSeller,
  makePurchaseAsSeller,
} from "./order-functions/seller-order-functions";

export { cancelOrder } from "./order-functions/general-order-functions";

export { withdrawFunds } from "./withdraw-functions";

export { withdrawRevenue } from "./revenue-functions";

export { authenticateWithTelegram } from "./telegram-auth-functions";

export { initAppConfig } from "./init-app-config";

export { changeUserData } from "./user-profile-functions";
