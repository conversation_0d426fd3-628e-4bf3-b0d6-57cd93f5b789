import { onCall, HttpsError } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus } from "../types";
import { hasAvailableBalance, lockFunds } from "../balance-service";
import { getAppConfig } from "../fee-service";
import { getNextCounterValue } from "../counter-service";
import { DEFAULT_BUYER_LOCK_PERCENTAGE } from "../constants";
import { safeMultiply } from "../utils";

export const createOrderAsBuyer = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { buyerId, collectionId, amount, owned_gift_id } = request.data;

  if (!buyerId || !collectionId || !amount || amount <= 0) {
    throw new HttpsError(
      "invalid-argument",
      "buyerId, collectionId, and valid amount are required."
    );
  }

  if (request.auth.uid !== buyerId) {
    throw new HttpsError(
      "permission-denied",
      "You can only create orders for yourself as buyer."
    );
  }

  try {
    const db = admin.firestore();

    // Get collection to validate floor price
    const collectionDoc = await db
      .collection("collections")
      .doc(collectionId)
      .get();
    if (!collectionDoc.exists) {
      throw new HttpsError("not-found", "Collection not found.");
    }

    const collection = collectionDoc.data();
    if (amount < collection?.floorPrice) {
      throw new HttpsError(
        "invalid-argument",
        `Order amount must be at least ${collection?.floorPrice} TON (collection floor price).`
      );
    }

    // Get buyer lock percentage from app config
    const config = await getAppConfig();
    const buyerLockPercentage =
      config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
    const lockedAmount = safeMultiply(amount, buyerLockPercentage);

    // Check if buyer has sufficient balance
    const hasBalance = await hasAvailableBalance(buyerId, lockedAmount);
    if (!hasBalance) {
      throw new HttpsError(
        "failed-precondition",
        `Insufficient balance. You need at least ${lockedAmount} TON available (${
          buyerLockPercentage * 100
        }% of ${amount} TON order amount).`
      );
    }

    // Lock buyer percentage of order amount for buyer
    await lockFunds(buyerId, lockedAmount);

    // Get next order number
    const orderNumber = await getNextCounterValue("order_number");

    const orderData: Omit<OrderEntity, "id"> = {
      number: orderNumber,
      buyerId,
      collectionId,
      amount,
      status: OrderStatus.ACTIVE,
      owned_gift_id,
      createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
      updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
    };

    const orderRef = await db.collection("orders").add(orderData);

    return {
      success: true,
      orderId: orderRef.id,
      message: `Order created successfully with ${lockedAmount} TON locked (${
        buyerLockPercentage * 100
      }% of ${amount} TON order)`,
    };
  } catch (error) {
    console.error("Error creating order as buyer:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating order."
    );
  }
});

export const makePurchaseAsBuyer = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { buyerId, orderId } = request.data;

  if (!buyerId || !orderId) {
    throw new HttpsError(
      "invalid-argument",
      "buyerId and orderId are required."
    );
  }

  if (request.auth.uid !== buyerId) {
    throw new HttpsError(
      "permission-denied",
      "You can only make purchases for yourself."
    );
  }

  try {
    const db = admin.firestore();

    // Get order
    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Check if order is available for purchase
    if (order.status !== OrderStatus.ACTIVE) {
      throw new HttpsError(
        "failed-precondition",
        "Order is not available for purchase."
      );
    }

    // Check if order already has a buyer
    if (order.buyerId && order.buyerId !== buyerId) {
      throw new HttpsError("failed-precondition", "Order already has a buyer.");
    }

    // Check if buyer is trying to buy their own order
    if (order.sellerId === buyerId) {
      throw new HttpsError(
        "failed-precondition",
        "You cannot buy your own order."
      );
    }

    // Get buyer lock percentage from app config
    const config = await getAppConfig();
    const buyerLockPercentage =
      config?.buyer_lock_percentage ?? DEFAULT_BUYER_LOCK_PERCENTAGE;
    const lockedAmount = safeMultiply(order.amount, buyerLockPercentage);

    // Check if buyer has sufficient balance
    const hasBalance = await hasAvailableBalance(buyerId, lockedAmount);
    if (!hasBalance) {
      throw new HttpsError(
        "failed-precondition",
        `Insufficient balance. You need at least ${lockedAmount} TON available (${
          buyerLockPercentage * 100
        }% of ${order.amount} TON order amount).`
      );
    }

    // Lock buyer percentage of order amount
    await lockFunds(buyerId, lockedAmount);

    // Update order with buyer and change status to paid
    await db.collection("orders").doc(orderId).update({
      buyerId,
      status: OrderStatus.PAID,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message: `Purchase successful! ${lockedAmount} TON locked (${
        buyerLockPercentage * 100
      }% of ${order.amount} TON order). Waiting for seller to send gift.`,
      lockedAmount,
      orderAmount: order.amount,
      lockPercentage: buyerLockPercentage * 100,
    };
  } catch (error) {
    console.error("Error making purchase as buyer:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while making purchase."
    );
  }
});
