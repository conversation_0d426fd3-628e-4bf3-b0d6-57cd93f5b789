import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { OrderEntity } from "./types";
import { spendLockedFunds, updateUserBalance } from "./balance-service";
import { getAppConfig, applyFeeToMarketplaceRevenue } from "./fee-service";
import {
  DEFAULT_SELLER_LOCK_PERCENTAGE,
  DEFAULT_REJECT_ORDER_FEE_BPS,
  BPS_DIVISOR,
} from "./constants";
import { safeMultiply, safeDivide, safeSubtract } from "./utils";

const db = admin.firestore();

/**
 * Process expired orders - applies same logic as seller rejection (case 2)
 * This function handles orders where:
 * - deadline < current date
 * - status = "paid"
 * - has both buyerId and sellerId
 */
export async function processExpiredOrders(): Promise<void> {
  try {
    console.log("Starting expired orders processing...");

    const now = admin.firestore.Timestamp.now();

    // Query for expired orders
    const expiredOrdersQuery = db
      .collection("orders")
      .where("status", "==", "paid")
      .where("deadline", "<", now);

    const expiredOrdersSnapshot = await expiredOrdersQuery.get();

    if (expiredOrdersSnapshot.empty) {
      console.log("No expired orders found");
      return;
    }

    console.log(
      `Found ${expiredOrdersSnapshot.size} expired orders to process`
    );

    const config = await getAppConfig();
    const rejectFeePercentage =
      config?.reject_order_fee ?? DEFAULT_REJECT_ORDER_FEE_BPS;

    // Process each expired order
    for (const orderDoc of expiredOrdersSnapshot.docs) {
      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Validate order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        console.log(`Skipping order ${order.id}: missing buyer or seller`);
        continue;
      }

      try {
        await processExpiredOrder(order, rejectFeePercentage);
        console.log(`Successfully processed expired order ${order.id}`);
      } catch (error) {
        console.error(`Failed to process expired order ${order.id}:`, error);
        // Continue processing other orders even if one fails
      }
    }

    console.log("Expired orders processing completed");
  } catch (error) {
    console.error("Error in processExpiredOrders:", error);
    throw error;
  }
}

async function processExpiredOrder(
  order: OrderEntity,
  rejectFeePercentage: number
): Promise<void> {
  // Get seller lock percentage from app config
  const config = await getAppConfig();
  const sellerLockPercentage =
    config?.seller_lock_percentage ?? DEFAULT_SELLER_LOCK_PERCENTAGE;
  const sellerLockedAmount = safeMultiply(order.amount, sellerLockPercentage);

  // Apply same logic as Case 2: Seller rejects order with buyer (order status is "paid")
  // Seller loses their locked amount (configurable % of order)
  // From the order amount: reject_order_fee% goes to marketplace, remaining goes to buyer
  const marketplaceFee = safeDivide(
    safeMultiply(order.amount, rejectFeePercentage),
    BPS_DIVISOR
  );
  const buyerCompensation = safeSubtract(order.amount, marketplaceFee);

  // TODO fix this function
  // Transfer funds
  if (order.sellerId) {
    await spendLockedFunds(order.sellerId, sellerLockedAmount); // Seller loses their locked amount
  }

  await updateUserBalance(order.buyerId!, buyerCompensation, 0); // Buyer gets order amount - marketplace fee
  await applyFeeToMarketplaceRevenue(marketplaceFee, "expired_order_penalty");

  // Delete the expired order
  await db.collection("orders").doc(order.id).delete();

  console.log(
    `Expired order ${order.id} processed: ` +
      `Seller lost ${sellerLockedAmount} TON (${safeMultiply(
        sellerLockPercentage,
        100
      )}% of order), ` +
      `Buyer received ${buyerCompensation} TON, ` +
      `Marketplace fee: ${marketplaceFee} TON`
  );
}

export const expiredOrdersMonitor = onSchedule(
  {
    schedule: "0 0 * * *", // Run daily at midnight UTC
    timeZone: "UTC",
  },
  async () => {
    try {
      console.log(
        "Expired orders monitor triggered at:",
        new Date().toISOString()
      );
      await processExpiredOrders();
      console.log("Expired orders monitor completed successfully");
    } catch (error) {
      console.error("Expired orders monitor failed:", error);
    }
  }
);
